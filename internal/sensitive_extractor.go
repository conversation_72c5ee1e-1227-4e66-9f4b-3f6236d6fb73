package internal

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/dop251/goja"
)

// OriginalFindSomethingExtractor 基于goja引擎的敏感信息提取器
// 使用原版findsomething的JavaScript代码进行敏感信息提取
type OriginalFindSomethingExtractor struct {
	vm *goja.Runtime // goja JavaScript运行时
}

// NewOriginalFindSomethingExtractor 创建新的敏感信息提取器实例
// 初始化goja运行时并加载JavaScript提取代码
// 如果无法读取或执行JavaScript文件，将返回nil
//
// 返回值：
//   - *OriginalFindSomethingExtractor: 敏感信息提取器实例，失败时返回nil
func NewOriginalFindSomethingExtractor() *OriginalFindSomethingExtractor {
	vm := goja.New()

	// 尝试读取JavaScript提取代码
	var jsCode []byte
	var err error

	// 尝试从assets目录读取findsomething核心提取文件
	jsPath := filepath.Join("assets", "findsomething_core.js")
	jsCode, err = os.ReadFile(jsPath)
	if err != nil {
		// 如果读取失败，返回nil（必须依赖外部JavaScript文件）
		fmt.Printf("❌ 读取JavaScript文件失败: %v\n", err)
		return nil
	}

	// 直接使用核心提取代码，无需适配
	adaptedCode := string(jsCode)

	// 调试：输出适配后的代码片段
	fmt.Printf("📝 适配后的JavaScript代码长度: %d 字符\n", len(adaptedCode))

	// 在goja中执行JavaScript代码
	_, err = vm.RunString(adaptedCode)
	if err != nil {
		// 如果执行失败，返回nil（必须依赖外部JavaScript文件）
		fmt.Printf("❌ 执行JavaScript代码失败: %v\n", err)
		// 输出前500个字符用于调试
		if len(adaptedCode) > 500 {
			fmt.Printf("📝 适配后代码前500字符:\n%s\n", adaptedCode[:500])
		} else {
			fmt.Printf("📝 完整适配后代码:\n%s\n", adaptedCode)
		}
		return nil
	}

	return &OriginalFindSomethingExtractor{
		vm: vm,
	}
}

// ExtractSensitiveInfo 从给定的内容中提取敏感信息
// 使用JavaScript引擎执行提取逻辑，完全兼容findsomething的提取规则
//
// 参数：
//   - content: 要分析的内容（HTML或JavaScript代码）
//   - sourceURL: 内容来源URL
//
// 返回值：
//   - *FindSomethingResult: 提取到的敏感信息结果
func (e *OriginalFindSomethingExtractor) ExtractSensitiveInfo(content, sourceURL string) *FindSomethingResult {
	if e.vm == nil {
		return nil
	}
	
	// 设置全局变量
	e.vm.Set("inputData", content)
	e.vm.Set("sourceURL", sourceURL)
	
	// 执行提取函数
	result, err := e.vm.RunString(`
		(function() {
			try {
				// 使用extract_info函数提取敏感信息
				var extractedData = extract_info(inputData);
				
				// 构建符合FindSomethingResult格式的结果
				var result = {
					current: sourceURL,
					done: "completed",
					tasklist: [],
					donetasklist: [],
					pretasknum: 1,
					source: {},
					ip: extractedData.ip || [],
					ip_port: extractedData.ip_port || [],
					domain: extractedData.domain || [],
					path: extractedData.path || [],
					incomplete_path: extractedData.incomplete_path || [],
					url: extractedData.url || [],
					sfz: extractedData.sfz || [],
					mobile: extractedData.mobile || [],
					mail: extractedData.mail || [],
					jwt: extractedData.jwt || [],
					algorithm: extractedData.algorithm || [],
					secret: extractedData.secret || [],
					static: extractedData.static || []
				};
				
				// 设置source映射
				result.source[sourceURL] = sourceURL;
				
				return result;
			} catch (e) {
				// 如果提取失败，返回空结果
				return {
					current: sourceURL,
					done: "error",
					tasklist: [],
					donetasklist: [],
					pretasknum: 0,
					source: {},
					ip: [],
					ip_port: [],
					domain: [],
					path: [],
					incomplete_path: [],
					url: [],
					sfz: [],
					mobile: [],
					mail: [],
					jwt: [],
					algorithm: [],
					secret: [],
					static: []
				};
			}
		})()
	`)
	
	if err != nil {
		return nil
	}
	
	// 将JavaScript结果转换为Go结构体
	resultObj := result.Export()
	jsonBytes, err := json.Marshal(resultObj)
	if err != nil {
		return nil
	}
	
	var findSomethingResult FindSomethingResult
	err = json.Unmarshal(jsonBytes, &findSomethingResult)
	if err != nil {
		return nil
	}
	
	return &findSomethingResult
}

// adaptJSForGoja 适配JavaScript代码以在goja环境中运行
// 移除浏览器特定的API调用，保留核心提取逻辑
func adaptJSForGoja(jsCode string) string {
	// 使用更精确的正则表达式替换，保持语法结构完整

	// 1. 替换chrome API调用为空函数
	chromeRegex := regexp.MustCompile(`chrome\.[a-zA-Z.]+\([^)]*\)`)
	adapted := chromeRegex.ReplaceAllString(jsCode, "function(){}")

	// 2. 替换chrome.storage.local.get为模拟函数
	chromeStorageRegex := regexp.MustCompile(`chrome\.storage\.local\.get\(([^,]+),\s*function\(([^)]*)\)\s*\{`)
	adapted = chromeStorageRegex.ReplaceAllString(adapted, "setTimeout(function(){var $2={}; ")

	// 3. 替换fetch调用为空Promise
	fetchRegex := regexp.MustCompile(`fetch\([^)]*\)`)
	adapted = fetchRegex.ReplaceAllString(adapted, "Promise.resolve({})")

	// 4. 替换Headers构造函数
	headersRegex := regexp.MustCompile(`new Headers\(\)`)
	adapted = headersRegex.ReplaceAllString(adapted, "{}")

	// 5. 替换Request构造函数
	requestRegex := regexp.MustCompile(`new Request\([^)]*\)`)
	adapted = requestRegex.ReplaceAllString(adapted, "{}")

	// 6. 替换Headers.append调用
	appendRegex := regexp.MustCompile(`[a-zA-Z]+\.append\([^)]*\)`)
	adapted = appendRegex.ReplaceAllString(adapted, "void(0)")

	// 7. 替换console.log
	consoleRegex := regexp.MustCompile(`console\.log\([^)]*\)`)
	adapted = consoleRegex.ReplaceAllString(adapted, "void(0)")

	// 8. 替换Promise相关调用
	promiseRegex := regexp.MustCompile(`Promise\.[a-zA-Z]+\([^)]*\)`)
	adapted = promiseRegex.ReplaceAllString(adapted, "Promise.resolve({})")

	return adapted
}

// extractCoreJSFunctions 从原版background.js中提取核心敏感信息提取函数
func extractCoreJSFunctions(jsCode string) string {
	// 提取需要的变量和函数
	var coreJS strings.Builder

	// 1. 提取静态变量
	staticFileRegex := regexp.MustCompile(`var static_file = \[[^\]]+\];`)
	if match := staticFileRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	nonStaticFileRegex := regexp.MustCompile(`var non_static_file = \[[^\]]+\]`)
	if match := nonStaticFileRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	keyRegex := regexp.MustCompile(`var key = \[[^\]]+\];`)
	if match := keyRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	notSubKeyRegex := regexp.MustCompile(`var not_sub_key = \[[^\]]+\];`)
	if match := notSubKeyRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	// 2. 提取nuclei_regex数组（完整的）
	nucleiRegex := regexp.MustCompile(`(?s)var nuclei_regex = \[[^\]]+\];`)
	if match := nucleiRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	// 3. 提取辅助函数 - 使用更精确的正则表达式匹配完整函数
	addFuncRegex := regexp.MustCompile(`(?s)function add\(arr1,arr2\) \{(?:[^{}]|\{[^}]*\})*\}`)
	if match := addFuncRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	sub1FuncRegex := regexp.MustCompile(`(?s)function sub_1\(arr1\) \{(?:[^{}]|\{[^}]*\})*\}`)
	if match := sub1FuncRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	getSecretFuncRegex := regexp.MustCompile(`(?s)function get_secret\(data\) \{(?:[^{}]|\{[^}]*\})*\}`)
	if match := getSecretFuncRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	// 4. 提取核心extract_info函数
	extractInfoRegex := regexp.MustCompile(`(?s)function extract_info\(data\) \{(?:[^{}]|\{[^}]*\})*\}`)
	if match := extractInfoRegex.FindString(jsCode); match != "" {
		coreJS.WriteString(match + "\n")
	}

	return coreJS.String()
}