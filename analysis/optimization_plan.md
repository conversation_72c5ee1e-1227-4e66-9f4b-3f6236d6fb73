# WebScan项目代码质量优化执行计划

## 🎯 总体目标

通过系统性重构优化WebScan项目的代码质量，实现：
- 减少25-30%代码重复
- 提升40-50%可维护性  
- 改善模块间耦合关系
- 建立清晰的架构边界

## 📅 执行时间表

**总工期**: 7-10个工作日  
**执行方式**: 分阶段渐进式重构  
**风险控制**: 每阶段完成后进行功能验证

## 🚀 第一阶段：基础工具类创建 (2-3天)

### 1.1 URL处理工具类
**文件**: `internal/utils/url_utils.go`
**目标**: 统一所有URL处理逻辑

```go
package utils

type URLProcessor struct {
    maxLength int
    schemes   []string
}

func NewURLProcessor() *URLProcessor {
    return &URLProcessor{
        maxLength: 2048,
        schemes:   []string{"http", "https"},
    }
}

// NormalizeURL 标准化URL
func (up *URLProcessor) NormalizeURL(rawURL string) string

// ValidateURL 验证URL有效性  
func (up *URLProcessor) ValidateURL(url string) bool

// DeduplicateURLs 批量去重URL
func (up *URLProcessor) DeduplicateURLs(urls []string) []string

// ResolveRelativeURL 解析相对URL
func (up *URLProcessor) ResolveRelativeURL(base, relative string) string
```

**替换位置**:
- `cmd/main.go:normalizeURL()` 
- `internal/database.go:CreateBatchTasks()` 中的去重逻辑
- `internal/scan.go:resolveRelativeURL()`

### 1.2 错误处理工具类
**文件**: `internal/utils/error_utils.go`
**目标**: 统一错误创建和处理模式

```go
package utils

type ErrorHelper struct {
    errorManager *core.ErrorManager
}

func NewErrorHelper() *ErrorHelper {
    return &ErrorHelper{
        errorManager: core.GetErrorManager(),
    }
}

// CreateScanError 创建扫描相关错误
func (eh *ErrorHelper) CreateScanError(code core.ErrorCode, message, url string, cause error) error

// CreateDBError 创建数据库相关错误
func (eh *ErrorHelper) CreateDBError(message string, cause error) error

// HandleError 统一错误处理
func (eh *ErrorHelper) HandleError(err error, context string)
```

**替换位置**:
- `internal/scan.go` 中的错误创建逻辑
- `internal/database.go` 中的错误处理
- `internal/browser_pool.go` 中的错误管理

### 1.3 配置访问器
**文件**: `internal/utils/config_accessor.go`  
**目标**: 统一配置读取接口

```go
package utils

type ConfigAccessor struct {
    scanConfig *config.ScanConfig
}

func NewConfigAccessor() *ConfigAccessor {
    return &ConfigAccessor{
        scanConfig: config.CurrentScanConfig,
    }
}

// GetTimeoutConfig 获取超时配置
func (ca *ConfigAccessor) GetTimeoutConfig() TimeoutSettings

// GetMemoryConfig 获取内存配置  
func (ca *ConfigAccessor) GetMemoryConfig() MemorySettings

// GetBrowserConfig 获取浏览器配置
func (ca *ConfigAccessor) GetBrowserConfig() BrowserSettings
```

## 🔧 第二阶段：常量统一管理 (1天)

### 2.1 常量定义文件
**文件**: `internal/constants/constants.go`
**目标**: 消除魔法数字，统一常量管理

```go
package constants

// 内存相关常量
const (
    DefaultMaxMemoryMB     = 100
    MinMemoryMB           = 10
    MemoryDivisionFactor  = 20
    DefaultMaxCache       = 1000
)

// 超时相关常量
const (
    DefaultPageTimeout    = 30 * time.Second
    DefaultRequestTimeout = 10 * time.Second
    BrowserGetTimeout     = 30 * time.Second
)

// 资源类型常量
var (
    StaticExtensions = []string{
        ".js", ".css", ".png", ".jpg", ".jpeg", ".gif", 
        ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot",
    }
    
    APIPatterns = []string{
        "/api/", "/ajax/", "/rest/", "/graphql", "/rpc/",
        "/service/", "/endpoint/", "/data/", "/query/",
    }
)
```

### 2.2 替换魔法数字
**修改文件**:
- `internal/scan.go` - 替换内存计算中的魔法数字
- `internal/memory_manager.go` - 替换默认内存限制
- `internal/scanner_api.go` - 使用统一的扩展名和模式定义

## ⚙️ 第三阶段：核心接口定义 (1-2天)

### 3.1 扫描器接口
**文件**: `internal/interfaces/scanner.go`

```go
package interfaces

type PageScanner interface {
    ScanPage(ctx context.Context, url string) (*ScanResult, error)
}

type ResourceExtractor interface {
    ExtractResources(page Page) ([]Resource, error)
}

type SensitiveAnalyzer interface {
    AnalyzeContent(content, sourceURL string) (*SensitiveInfo, error)
}

type LinkExtractor interface {
    ExtractLinks(page Page) ([]string, error)
}
```

### 3.2 存储接口
**文件**: `internal/interfaces/storage.go`

```go
package interfaces

type ScanResultStorage interface {
    SaveScanResult(result *ScanResult) error
    SaveBatchResults(results []*ScanResult) error
    GetScanHistory(limit int) ([]*ScanResult, error)
}

type SensitiveInfoStorage interface {
    SaveSensitiveInfo(info *SensitiveInfo) error
    QuerySensitiveInfo(filters map[string]interface{}) ([]*SensitiveInfo, error)
}
```

## 🏗️ 第四阶段：模块重构 (3-4天)

### 4.1 扫描引擎重构
**文件**: `internal/scan.go`
**目标**: 拆分scanSingle函数，职责分离

```go
// 新的扫描器结构
type PageScanner struct {
    resourceExtractor ResourceExtractor
    sensitiveAnalyzer SensitiveAnalyzer  
    linkExtractor     LinkExtractor
    configAccessor    *utils.ConfigAccessor
    errorHelper       *utils.ErrorHelper
}

// 拆分后的方法
func (ps *PageScanner) setupPageListeners(page Page) error
func (ps *PageScanner) extractPageContent(page Page) (*PageContent, error)  
func (ps *PageScanner) processSensitiveInfo(content string, url string) (*SensitiveInfo, error)
func (ps *PageScanner) buildScanResult(url string, content *PageContent, sensitive *SensitiveInfo) *ScanResult
```

### 4.2 敏感信息提取器优化
**文件**: `internal/sensitive_extractor.go`
**目标**: 简化正则表达式处理，提高可读性

```go
// 正则表达式管理器
type RegexManager struct {
    patterns map[string]*regexp.Regexp
}

func (rm *RegexManager) CompilePatterns() error {
    patterns := map[string]string{
        "static_file":     `var static_file = \[[^\]]+\];`,
        "non_static_file": `var non_static_file = \[[^\]]+\]`,
        "key":            `var key = \[[^\]]+\];`,
        "not_sub_key":    `var not_sub_key = \[[^\]]+\];`,
    }
    
    for name, pattern := range patterns {
        compiled, err := regexp.Compile(pattern)
        if err != nil {
            return err
        }
        rm.patterns[name] = compiled
    }
    return nil
}
```

### 4.3 输出管理模块合并
**目标**: 合并`output_manager.go`和`export.go`功能

```go
// 统一的输出管理器
type OutputManager struct {
    formatter ResultFormatter
    exporter  ResultExporter
    logger    *core.Logger
}

type ResultFormatter interface {
    FormatProgress(completed, total int) string
    FormatScanResult(result *ScanResult) string
    FormatError(err error) string
}

type ResultExporter interface {
    ExportJSON(results []*ScanResult, path string) error
    ExportCSV(results []*ScanResult, path string) error
    ExportHTML(results []*ScanResult, path string) error
}
```

## 🧪 第五阶段：测试和验证 (1天)

### 5.1 单元测试编写
为新创建的工具类编写单元测试：
- `utils/url_utils_test.go`
- `utils/error_utils_test.go`  
- `utils/config_accessor_test.go`

### 5.2 集成测试
验证重构后的功能完整性：
- 单URL扫描测试
- 批量URL扫描测试
- 敏感信息提取测试
- 数据库存储测试

### 5.3 性能基准测试
对比重构前后的性能指标：
- 内存使用量对比
- 扫描速度对比
- 并发处理能力对比

## 📋 详细任务清单

### 需要创建的文件
- [ ] `internal/utils/url_utils.go`
- [ ] `internal/utils/error_utils.go`
- [ ] `internal/utils/config_accessor.go`
- [ ] `internal/constants/constants.go`
- [ ] `internal/interfaces/scanner.go`
- [ ] `internal/interfaces/storage.go`

### 需要重构的文件
- [ ] `internal/scan.go` - 拆分scanSingle函数
- [ ] `internal/sensitive_extractor.go` - 优化正则处理
- [ ] `internal/output_manager.go` - 合并export功能
- [ ] `cmd/main.go` - 使用新工具类
- [ ] `internal/database.go` - 移除重复逻辑
- [ ] `internal/scanner_api.go` - 使用统一常量

### 需要删除的文件
- [ ] `internal/export.go` - 功能合并到output_manager

## ⚠️ 风险控制措施

### 1. 分阶段提交
每完成一个阶段立即提交代码，便于问题回滚

### 2. 功能验证
每个阶段完成后运行完整的功能测试

### 3. 性能监控  
重构过程中持续监控内存使用和扫描性能

### 4. 备份策略
重构前创建完整的代码备份

## 📊 成功指标

### 代码质量指标
- [ ] 代码重复率 < 5%
- [ ] 平均函数长度 < 30行
- [ ] 圈复杂度 < 10
- [ ] 测试覆盖率 > 70%

### 性能指标
- [ ] 内存使用减少 20%+
- [ ] 扫描速度提升 10%+
- [ ] 错误恢复能力提升 40%+

### 可维护性指标
- [ ] 新功能开发效率提升 30%+
- [ ] Bug修复时间减少 40%+
- [ ] 代码审查时间减少 35%+

---

**执行原则**: 渐进式重构，保持功能完整性，确保向后兼容，持续验证和测试。
